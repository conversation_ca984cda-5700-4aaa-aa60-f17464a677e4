from pydantic import BaseModel, EmailStr, Field, model_validator, field_validator
from typing import Optional
from datetime import datetime
import re
from app.regex.regex_patterns import AUTH_FIRST_NAME_PATTERN, AUTH_LAST_NAME_PATTERN, AUTH_EMAIL_PATTERN, AUTH_PASSWORD_PATTERN

class UserBase(BaseModel):
    email: EmailStr
    first_name: str = Field(..., pattern=AUTH_FIRST_NAME_PATTERN)
    last_name: str = Field(..., pattern=AUTH_LAST_NAME_PATTERN)
    status: Optional[bool] = True  # Renamed from 'is_active' to 'status'
    action: Optional[str] = None  # Added 'action'

class UserCreate(UserBase):
    password: str = Field(..., pattern=AUTH_PASSWORD_PATTERN)
    confirm_password: str = Field(..., pattern=AUTH_PASSWORD_PATTERN)

    @field_validator('password', 'confirm_password')
    @classmethod
    def validate_password_complexity(cls, v):
        if len(v) < 8:
            raise ValueError("Password must be at least 8 characters long")
        if not re.search(r"[a-z]", v):
            raise ValueError("Password must contain at least one lowercase letter")
        if not re.search(r"[A-Z]", v):
            raise ValueError("Password must contain at least one uppercase letter")
        if not re.search(r"\d", v):
            raise ValueError("Password must contain at least one number")
        if not re.search(r"[@$!%*?&]", v):
            raise ValueError("Password must contain at least one special character (@$!%*?&)")
        return v

    @model_validator(mode='after')
    def passwords_match(self):
        if self.password != self.confirm_password:
            raise ValueError("Passwords do not match")
        return self

class UserUpdate(BaseModel):
    email: Optional[EmailStr] = Field(..., pattern=AUTH_EMAIL_PATTERN)
    first_name: Optional[str] = Field(None, pattern=AUTH_FIRST_NAME_PATTERN)
    last_name: Optional[str] = Field(None, pattern=AUTH_LAST_NAME_PATTERN)
    password: Optional[str] = Field(None, pattern=AUTH_PASSWORD_PATTERN)
    status: Optional[bool] = None  # Renamed from 'is_active' to 'status'
    action: Optional[str] = None  # Added 'action'

    @field_validator('password')
    @classmethod
    def validate_password_complexity(cls, v):
        if v is None:
            return v
        if len(v) < 8:
            raise ValueError("Password must be at least 8 characters long")
        if not re.search(r"[a-z]", v):
            raise ValueError("Password must contain at least one lowercase letter")
        if not re.search(r"[A-Z]", v):
            raise ValueError("Password must contain at least one uppercase letter")
        if not re.search(r"\d", v):
            raise ValueError("Password must contain at least one number")
        if not re.search(r"[@$!%*?&]", v):
            raise ValueError("Password must contain at least one special character (@$!%*?&)")
        return v

class UserResponse(UserBase):
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    login_token: Optional[str] = None  # Added 'login_token'

    model_config = {"from_attributes": True}



admin:
  address:
    socket_address:
      protocol: TCP
      address: 0.0.0.0
      port_value: 9901

static_resources:
  listeners:
  - name: admin_listener
    address:
      socket_address:
        protocol: TCP
        address: 0.0.0.0
        port_value: 8081
    filter_chains:
    - filters:
      - name: envoy.filters.network.http_connection_manager
        typed_config:
          "@type": type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
          stat_prefix: admin_http
          access_log:
          - name: envoy.access_loggers.stdout
            typed_config:
              "@type": type.googleapis.com/envoy.extensions.access_loggers.stream.v3.StdoutAccessLog
          http_filters:
          - name: envoy.filters.http.cors
            typed_config:
              "@type": type.googleapis.com/envoy.extensions.filters.http.cors.v3.Cors
          - name: envoy.filters.http.local_ratelimit
            typed_config:
              "@type": type.googleapis.com/udpa.type.v1.TypedStruct
              type_url: type.googleapis.com/envoy.extensions.filters.http.local_ratelimit.v3.LocalRateLimit
              value:
                stat_prefix: admin_rate_limiter
                token_bucket:
                  max_tokens: 50
                  tokens_per_fill: 10
                  fill_interval: 1s
                filter_enabled:
                  runtime_key: admin_rate_limit_enabled
                  default_value:
                    numerator: 100
                    denominator: HUNDRED
                filter_enforced:
                  runtime_key: admin_rate_limit_enforced
                  default_value:
                    numerator: 100
                    denominator: HUNDRED
          - name: envoy.filters.http.router
            typed_config:
              "@type": type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
          route_config:
            name: admin_route
            virtual_hosts:
            - name: admin_service
              domains: ["*"]
              cors:
                allow_origin_string_match:
                - prefix: "*"
                allow_methods: GET, PUT, DELETE, POST, OPTIONS
                allow_headers: keep-alive,user-agent,cache-control,content-type,content-transfer-encoding,custom-header-1,x-accept-content-transfer-encoding,x-accept-response-streaming,x-user-agent,x-grpc-web,grpc-timeout,authorization,accept
                max_age: "1728000"
                expose_headers: custom-header-1,grpc-status,grpc-message
                allow_credentials: true
              routes:
              - match:
                  prefix: "/api/v1/admin/"
                route:
                  cluster: admin-service
                  prefix_rewrite: "/api/v1/"
                  timeout: 60s
              - match:
                  prefix: "/admin/"
                route:
                  cluster: admin-service
                  prefix_rewrite: "/"
                  timeout: 60s
              - match:
                  path: "/docs"
                route:
                  cluster: admin-service
                  timeout: 60s
                  prefix_rewrite: "/docs"
              - match:
                  path: "/redoc"
                route:
                  cluster: admin-service
                  timeout: 60s
                  prefix_rewrite: "/redoc"
              - match:
                  exact_path: "/openapi.json"
                route:
                  cluster: admin-service
                  timeout: 60s
                  prefix_rewrite: "/openapi.json"
              - match:
                  prefix: "/health"
                direct_response:
                  status: 200
                  body:
                    inline_string: "Envoy Admin Gateway - healthy"
              - match:
                  prefix: "/"
                route:
                  cluster: admin-service
                  timeout: 60s

  clusters:
  - name: admin-service
    connect_timeout: 30s
    type: LOGICAL_DNS
    dns_lookup_family: V4_ONLY
    lb_policy: ROUND_ROBIN
    load_assignment:
      cluster_name: admin-service
      endpoints:
      - lb_endpoints:
        - endpoint:
            address:
              socket_address:
                address: admin-service
                port_value: 8000
    health_checks:
      - timeout: 5s
        interval: 10s
        unhealthy_threshold: 3
        healthy_threshold: 2
        http_health_check:
          path: "/health"

# Regex patterns for validation

# Name: Only letters and spaces, at least 2 characters
DOMAIN_NAME_REGEX = r"^[A-Za-z ]{2,}$"

# Code: Letters, numbers, dashes, and underscores (e.g., ABC123, abc_456-def)
DOMAIN_CODE_REGEX = r"^[A-Za-z0-9_-]+$"

# Description: Letters, numbers, spaces, basic punctuation, 10 to 500 characters
DOMAIN_DESCRIPTION_REGEX = r"^[A-Za-z0-9 .,;:!?'\"()\-]{10,500}$"

# Name: Only letters and spaces, at least 2 characters
APPLICATION_NAME_REGEX = r"^[A-Za-z ]{2,}$"

# Code: Letters, numbers, dashes, and underscores (e.g., ABC123, abc_456-def)
APPLICATION_CODE_REGEX = r"^[A-Za-z0-9_-]+$"

# Description: Letters, numbers, spaces, basic punctuation, 10 to 500 characters
APPLICATION_DESCRIPTION_REGEX = r"^[A-Za-z0-9 .,;:!?'\"()\-]{10,500}$"

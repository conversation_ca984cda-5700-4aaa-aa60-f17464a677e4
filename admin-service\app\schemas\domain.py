from pydantic import BaseModel, Field
from typing import Optional
from datetime import datetime
from app.regex.regex_patterns import DOMAIN_NAME_REGEX, DOMAIN_CODE_REGEX, DOMAIN_DESCRIPTION_REGEX

class DomainBase(BaseModel):
    domain_name: str = Field(..., pattern=DOMAIN_NAME_REGEX)  # Updated from 'name'
    domain_code: str = Field(..., pattern=DOMAIN_CODE_REGEX)   # Added 'domain_code'
    description: Optional[str] = Field(None, pattern=DOMAIN_DESCRIPTION_REGEX)  # Remains unchanged
    status: Optional[bool] = True  # Renamed from 'is_active'
    action: Optional[str] = None  # Added 'action'

class DomainCreate(DomainBase):
    pass  # No changes required

class DomainUpdate(BaseModel):
    domain_name: Optional[str] = Field(None, pattern=DOMAIN_NAME_REGEX)  # Updated from 'name'
    domain_code: Optional[str] = Field(None, pattern=DOMAIN_CODE_REGEX)   # Added 'domain_code'
    description: Optional[str] = Field(None, pattern=DOMAIN_DESCRIPTION_REGEX)  # Remains unchanged
    status: Optional[bool] = None  # Renamed from 'is_active'
    action: Optional[str] = None  # Added 'action'

class DomainResponse(DomainBase):
    id: int  # Remains unchanged
    created_at: datetime  # Remains unchanged
    updated_at: Optional[datetime] = None  # Remains unchanged

    class Config:
        from_attributes = True  # Updated to use the new Pydantic v2 configuration

apiVersion: apps/v1
kind: Deployment
metadata:
  name: admin-service
spec:
  replicas: 1
  template:
    spec:
      containers:
      - name: admin-service
        resources:
          limits:
            cpu: "500m"
            memory: "512Mi"
          requests:
            cpu: "100m"
            memory: "128Mi"
        env:
        - name: ENVIRONMENT
          valueFrom:
            configMapKeyRef:
              name: admin-service-config
              key: ENVIRONMENT
        - name: LOG_LEVEL
          valueFrom:
            configMapKeyRef:
              name: admin-service-config
              key: LOG_LEVEL
apiVersion: apps/v1
kind: Deployment
metadata:
  name: admin-service
spec:
  replicas: 2
  template:
    spec:
      containers:
      - name: admin-service
        resources:
          limits:
            cpu: "750m"
            memory: "768Mi"
          requests:
            cpu: "300m"
            memory: "384Mi"
        env:
        - name: ENVIRONMENT
          valueFrom:
            configMapKeyRef:
              name: admin-service-config
              key: ENVIRONMENT
        - name: LOG_LEVEL
          valueFrom:
            configMapKeyRef:
              name: admin-service-config
              key: LOG_LEVEL